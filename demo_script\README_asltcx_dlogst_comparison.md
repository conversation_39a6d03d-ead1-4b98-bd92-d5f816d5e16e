# ASLTCX vs DLOGST Comparison Tool

## Overview

This comparison tool evaluates the performance and functionality of two different time-frequency analysis implementations:

- **ASLTCX** (`code/asltcx.py`): A modified adaptive superlet transform implementation that outputs complex spectrum
- **DLOGST** (`reference/dlogst_spec_descriptor.py`): A reference implementation using frequency-dependent Gaussian windows with FFTW

## Files

### Main Comparison Script
- `asltcx_dlogst_comparison.py`: Main comparison script with GUI interface
- `test_asltcx_dlogst_comparison.py`: Test script to verify functionality without GUI

### Generated Outputs
- `asltcx_dlogst_comparison_[trace_name].png`: Comparison plots for each analyzed trace

## Features

### Data Sources
- **SEG-Y files**: Load and analyze real seismic data
- **Synthetic data**: Generate test signals with multiple frequency components for controlled testing

### Comparison Methodology
The tool creates 7-column comparison plots showing:

1. **Original Signal**: Input time series
2. **ASLTCX Magnitude**: Time-frequency magnitude representation
3. **ASLTCX Voice**: Real component showing oscillatory behavior
4. **ASLTCX Mag×Voice**: Combined magnitude and voice product
5. **DLOGST Magnitude**: Time-frequency magnitude representation
6. **DLOGST Voice**: Voice component with phase information
7. **DLOGST Mag×Voice**: Combined magnitude and voice product

### Performance Metrics
- **Execution time comparison**: Measures computational efficiency
- **Speed ratio calculation**: Quantifies relative performance
- **Per-trace and overall summaries**: Detailed performance breakdown

## Usage

### Running the Main Comparison
```bash
python demo_script/asltcx_dlogst_comparison.py
```

### Running the Test Script
```bash
python demo_script/test_asltcx_dlogst_comparison.py
```

## Configuration Parameters

### ASLTCX Parameters
- **Ncyc**: Number of cycles (default: 3)
- **Order min/max**: Order range for superresolution (default: 1-10)

### DLOGST Parameters
- **Shape**: Shape constant for Gaussian window (default: 0.35)
- **Kmax**: Maximum k constant (default: 120)
- **Int_val**: Intercept constant (default: 35)

### Plot Settings
- **Frequency range**: Analysis frequency band
- **Time range**: Display time window
- **Colormap**: Visualization color scheme
- **Amplitude limits**: Display ranges for different components

## Test Results

Based on synthetic data testing:

### Performance Comparison
- **ASLTCX**: ~0.34 seconds (faster)
- **DLOGST**: ~1.25 seconds 
- **Speed ratio**: DLOGST is ~3.7x slower than ASLTCX

### Output Characteristics
- **ASLTCX**: 
  - Focused frequency resolution (50 frequency bins)
  - Efficient computation
  - Good time-frequency localization
  
- **DLOGST**: 
  - High frequency resolution (1000+ frequency bins)
  - More detailed spectral analysis
  - Additional spectral descriptors (centroid, slope, decrease)

## Key Differences

### Computational Approach
- **ASLTCX**: Uses adaptive superlet wavelets with multiplicative/additive superresolution
- **DLOGST**: Uses frequency-dependent Gaussian windows with FFTW optimization

### Output Resolution
- **ASLTCX**: Configurable frequency resolution based on input parameters
- **DLOGST**: High-resolution frequency analysis up to Nyquist frequency

### Additional Features
- **ASLTCX**: Focus on efficient time-frequency representation
- **DLOGST**: Comprehensive spectral descriptors including peak frequency, spectral centroid, slope, and decrease measures

## Dependencies

### Required Packages
- `numpy`: Numerical computations
- `matplotlib`: Plotting and visualization
- `segyio`: SEG-Y file handling
- `tkinter`: GUI interface
- `pyfftw`: Fast Fourier transforms (for DLOGST)
- `scipy`: Scientific computing (for DLOGST interpolation)

### Installation
```bash
pip install numpy matplotlib segyio pyfftw scipy
```

## Structure Based On

This comparison tool follows the structure and methodology of:
- `demo_script/segy_fasltcx_dlogst_comparison.py`: Reference comparison implementation

The new tool adapts the proven comparison framework to evaluate ASLTCX vs DLOGST implementations while maintaining consistency in:
- User interface design
- Parameter configuration
- Visualization approach
- Performance measurement
- Output generation

## Usage Tips

1. **Start with synthetic data** to understand the differences between methods
2. **Adjust frequency ranges** based on your signal characteristics
3. **Compare execution times** for your specific use case
4. **Experiment with parameters** to optimize for your application
5. **Use multiple traces** to assess consistency across different signals

## Troubleshooting

### Import Errors
- Ensure all dependencies are installed
- Check that `code/` and `reference/` directories contain the required files
- Verify Python path configuration

### Performance Issues
- Reduce frequency resolution for faster computation
- Limit time window for large datasets
- Consider using synthetic data for initial testing

### Display Issues
- Adjust plot limits if visualizations appear blank
- Check colormap settings for better contrast
- Verify time axis orientation (seismic convention)
