#!/usr/bin/env python3
"""
Test script for ASLTCX vs DLOGST comparison functionality.
This script tests the basic functionality without GUI components.
"""

import numpy as np
import sys
import os

# Add the code and reference directories to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
reference_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'reference')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)
if reference_dir not in sys.path:
    sys.path.insert(0, reference_dir)

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from asltcx import asltcx
        print("✓ Successfully imported asltcx")
    except ImportError as e:
        print(f"✗ Failed to import asltcx: {e}")
        return False
    
    try:
        from dlogst_spec_descriptor import dlogst_spec_descriptor
        print("✓ Successfully imported dlogst_spec_descriptor")
    except ImportError as e:
        print(f"✗ Failed to import dlogst_spec_descriptor: {e}")
        return False
    
    return True

def test_synthetic_data_generation():
    """Test synthetic data generation."""
    print("\nTesting synthetic data generation...")
    
    try:
        # Create synthetic signal with multiple frequency components
        fs = 1000  # Sampling frequency
        duration = 2.0  # Duration in seconds
        t = np.linspace(0, duration, int(fs * duration))
        
        # Multi-component signal
        signal = (np.sin(2 * np.pi * 10 * t) * np.exp(-t/0.5) +  # Decaying 10 Hz
                  0.5 * np.sin(2 * np.pi * 25 * t) * (t > 0.5) * (t < 1.5) +  # 25 Hz burst
                  0.3 * np.sin(2 * np.pi * 50 * t) * np.exp(-(t-1)**2/0.1))  # Gaussian-modulated 50 Hz
        
        # Add some noise
        signal += 0.1 * np.random.randn(len(t))
        
        dt = 1.0 / fs
        
        print(f"✓ Generated synthetic signal:")
        print(f"  Duration: {duration} seconds")
        print(f"  Sampling rate: {fs} Hz")
        print(f"  Number of samples: {len(signal)}")
        print(f"  Signal range: [{np.min(signal):.3f}, {np.max(signal):.3f}]")
        
        return signal, t, dt
        
    except Exception as e:
        print(f"✗ Failed to generate synthetic data: {e}")
        return None, None, None

def test_asltcx_transform(signal, fs):
    """Test ASLTCX transform."""
    print("\nTesting ASLTCX transform...")
    
    try:
        from asltcx import asltcx
        
        # Parameters
        freqs = np.linspace(1, 100, 50)  # Frequency range
        ncyc = 3  # Number of cycles
        orders = (1, 5)  # Order range
        
        # Compute transform
        import time
        start_time = time.time()
        spectrum = asltcx(signal, fs, freqs, ncyc, orders, mult=False)
        execution_time = time.time() - start_time
        
        magnitude = np.abs(spectrum)
        voice = np.real(spectrum)
        
        print(f"✓ ASLTCX transform completed:")
        print(f"  Execution time: {execution_time:.4f} seconds")
        print(f"  Output shape: {spectrum.shape}")
        print(f"  Magnitude range: [{np.min(magnitude):.6f}, {np.max(magnitude):.6f}]")
        print(f"  Voice range: [{np.min(voice):.6f}, {np.max(voice):.6f}]")
        
        return spectrum, execution_time
        
    except Exception as e:
        print(f"✗ ASLTCX transform failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_dlogst_transform(signal, dt):
    """Test DLOGST transform."""
    print("\nTesting DLOGST transform...")
    
    try:
        from dlogst_spec_descriptor import dlogst_spec_descriptor
        
        # Parameters
        fmax_samples = len(signal) // 4  # Limit frequency range
        shape = 0.35
        kmax = 120
        int_val = 35
        
        # Compute transform
        import time
        start_time = time.time()
        MST, mag, phase, voice, peak_freq, freq_loc, spec_centroid, \
        spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_array, freqst = \
            dlogst_spec_descriptor(signal, dt, fmax_samples, shape, kmax, int_val)
        execution_time = time.time() - start_time
        
        print(f"✓ DLOGST transform completed:")
        print(f"  Execution time: {execution_time:.4f} seconds")
        print(f"  MST shape: {MST.shape}")
        print(f"  Magnitude range: [{np.min(mag):.6f}, {np.max(mag):.6f}]")
        print(f"  Voice range: [{np.min(voice):.6f}, {np.max(voice):.6f}]")
        print(f"  Frequency range: [{freqst[0]:.2f}, {freqst[-1]:.2f}] Hz")
        
        return MST, mag, voice, execution_time
        
    except Exception as e:
        print(f"✗ DLOGST transform failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def test_performance_comparison():
    """Test performance comparison between the two methods."""
    print("\n" + "="*60)
    print("PERFORMANCE COMPARISON TEST")
    print("="*60)
    
    # Test imports
    if not test_imports():
        return False
    
    # Generate test data
    signal, time_array, dt = test_synthetic_data_generation()
    if signal is None:
        return False
    
    fs = 1.0 / dt
    
    # Test ASLTCX
    asltcx_spectrum, asltcx_time = test_asltcx_transform(signal, fs)
    if asltcx_spectrum is None:
        return False
    
    # Test DLOGST
    dlogst_mst, dlogst_mag, dlogst_voice, dlogst_time = test_dlogst_transform(signal, dt)
    if dlogst_mst is None:
        return False
    
    # Performance comparison
    print(f"\n" + "="*40)
    print("PERFORMANCE SUMMARY")
    print("="*40)
    print(f"ASLTCX execution time: {asltcx_time:.4f} seconds")
    print(f"DLOGST execution time: {dlogst_time:.4f} seconds")
    print(f"Speed ratio (DLOGST/ASLTCX): {dlogst_time/asltcx_time:.2f}x")
    
    if dlogst_time < asltcx_time:
        print("DLOGST is faster")
    else:
        print("ASLTCX is faster")
    
    print("="*40)
    print("✓ All tests completed successfully!")
    
    return True

if __name__ == "__main__":
    success = test_performance_comparison()
    if success:
        print("\n🎉 All tests passed! The comparison script should work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        sys.exit(1)
