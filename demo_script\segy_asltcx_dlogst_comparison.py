#!/usr/bin/env python3
"""
ASLTCX vs DLOGST Spectral Descriptor Comparison
Compares the performance and functionality of two different time-frequency analysis implementations.

Creates a 7-column comparison plot for each specified trace showing:
1. Signal
2-4. ASLTCX: Magnitude, Voice, Magnitude*Voice
5-7. DLOGST: Magnitude, Voice, Magnitude*Voice

This script compares the asltcx.py implementation against the dlogst_spec_descriptor.py reference.
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import MultiCursor
import segyio
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import sys
import os
import time

# Add the code and reference directories to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
reference_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'reference')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)
if reference_dir not in sys.path:
    sys.path.insert(0, reference_dir)

try:
    # Try direct imports first
    from asltcx import asltcx
except ImportError:
    try:
        # Try package-style imports
        from code.asltcx import asltcx
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Make sure you're running this script from the demo_script directory")
        print("and that the code directory contains asltcx.py")
        print(f"Code directory: {code_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

# Import dlogst_spec_descriptor
try:
    # Try direct import first (when reference dir is in sys.path)
    from dlogst_spec_descriptor import dlogst_spec_descriptor
except ImportError:
    try:
        # Try package-style import
        from reference.dlogst_spec_descriptor import dlogst_spec_descriptor
    except ImportError:
        print("Error: Could not import dlogst_spec_descriptor")
        print("Make sure the reference directory contains dlogst_spec_descriptor.py")
        print(f"Reference directory: {reference_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

def load_segy_data():
    """Load SEG-Y data using file dialog."""
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="Select SEG-Y file", 
        filetypes=[("SEG-Y files", "*.sgy"), ("All files", "*.*")]
    )
    if not file_path:
        print("No file selected. Exiting.")
        exit()
    
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segyio.tools.collect(segy.trace[:])
        dt = segy.bin[segyio.BinField.Interval] / 1_000_000
        num_samples = data.shape[1]
        time = np.arange(num_samples) * dt
    
    return data, time, dt

def generate_synthetic_data():
    """Generate synthetic test data for comparison."""
    # Create synthetic signal with multiple frequency components
    fs = 1000  # Sampling frequency
    duration = 2.0  # Duration in seconds
    t = np.linspace(0, duration, int(fs * duration))
    
    # Multi-component signal
    signal = (np.sin(2 * np.pi * 10 * t) * np.exp(-t/0.5) +  # Decaying 10 Hz
              0.5 * np.sin(2 * np.pi * 25 * t) * (t > 0.5) * (t < 1.5) +  # 25 Hz burst
              0.3 * np.sin(2 * np.pi * 50 * t) * np.exp(-(t-1)**2/0.1))  # Gaussian-modulated 50 Hz
    
    # Add some noise
    signal += 0.1 * np.random.randn(len(t))
    
    dt = 1.0 / fs
    return signal[np.newaxis, :], t, dt

def get_data_source():
    """Ask user to choose between SEG-Y file or synthetic data."""
    root = tk.Tk()
    root.title("Data Source Selection")
    
    choice = tk.StringVar(value="synthetic")
    
    def on_ok():
        root.quit()
        root.destroy()
    
    tk.Label(root, text="Choose data source:").pack(pady=10)
    
    tk.Radiobutton(root, text="Synthetic test data", variable=choice, value="synthetic").pack()
    tk.Radiobutton(root, text="SEG-Y file", variable=choice, value="segy").pack()
    
    tk.Button(root, text="OK", command=on_ok).pack(pady=10)
    
    root.mainloop()
    return choice.get()

def get_plot_settings():
    """Get plot settings and parameters from user."""
    root = tk.Tk()
    root.title("Set Plot Limits and Transform Parameters")

    settings = {}
    entries = {}
    
    def on_ok():
        for key, (min_entry, max_entry) in entries.items():
            try:
                min_val = float(min_entry.get())
                max_val = float(max_entry.get())
                settings[key] = (min_val, max_val)
            except ValueError:
                print(f"Invalid input for {key}. Using default values.")
        
        # Get single value parameters
        for key, entry in single_entries.items():
            try:
                settings[key] = float(entry.get())
            except ValueError:
                print(f"Invalid input for {key}. Using default values.")
        
        settings['colormap'] = colormap_var.get()
        root.quit()
        root.destroy()

    # Plot limit fields
    fields = [
        ("Input Signal", (-1000, 1000)),
        ("Frequency", (0, 100)),
        ("Time (Y-axis)", (0, 2)),
        ("Magnitude Range", (0, 1)),
        ("Voice Range", (-1, 1)),
        ("Mag*Voice Range", (-500, 500))
    ]

    for i, (name, default) in enumerate(fields):
        tk.Label(root, text=f"{name} Limits:").grid(row=i, column=0, sticky="e")
        min_entry = ttk.Entry(root)
        min_entry.insert(0, str(default[0]))
        min_entry.grid(row=i, column=1)
        tk.Label(root, text="to").grid(row=i, column=2)
        max_entry = ttk.Entry(root)
        max_entry.insert(0, str(default[1]))
        max_entry.grid(row=i, column=3)
        entries[name] = (min_entry, max_entry)

    # Colormap selection
    row_offset = len(fields)
    tk.Label(root, text="Select Colormap:").grid(row=row_offset, column=0, sticky="e")
    colormap_options = ['viridis', 'plasma', 'inferno', 'magma', 'cividis', 'jet', 'seismic']
    colormap_var = tk.StringVar(root)
    colormap_var.set(colormap_options[0])
    colormap_menu = ttk.OptionMenu(root, colormap_var, *colormap_options)
    colormap_menu.grid(row=row_offset, column=1, columnspan=3, sticky="ew")

    # Transform parameters
    single_entries = {}
    param_fields = [
        ("ASLTCX Ncyc", 3),
        ("ASLTCX order min", 1),
        ("ASLTCX order max", 10),
        ("DLOGST shape", 0.35),
        ("DLOGST kmax", 120),
        ("DLOGST int_val", 35)
    ]

    for i, (name, default) in enumerate(param_fields):
        row = row_offset + 1 + i
        tk.Label(root, text=f"{name}:").grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(root)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1)
        single_entries[name] = entry

    ttk.Button(root, text="OK", command=on_ok).grid(
        row=row_offset + len(param_fields) + 2, column=1, columnspan=2
    )

    root.mainloop()
    return settings

def create_7_column_comparison(signal, time, fs, trace_name, settings):
    """Create the 7-column comparison plot for a single trace."""

    dt = 1.0 / fs
    freq_range = settings['Frequency']
    fmax_samples = int(freq_range[1] * len(signal) / fs)

    # Frequency array for ASLTCX
    freqs = np.linspace(freq_range[0], freq_range[1], 80)

    # ASLTCX parameters
    ncyc = int(settings['ASLTCX Ncyc'])
    orders = (int(settings['ASLTCX order min']), int(settings['ASLTCX order max']))

    # DLOGST parameters
    shape = settings['DLOGST shape']
    kmax = settings['DLOGST kmax']
    int_val = settings['DLOGST int_val']

    print(f"Computing ASLTCX Transform for {trace_name}...")
    start_time = time.time()
    # Compute ASLTCX Transform
    asltcx_spectrum = asltcx(signal, fs, freqs, ncyc, orders, mult=False)
    asltcx_time = time.time() - start_time
    
    asltcx_magnitude = np.abs(asltcx_spectrum)
    asltcx_voice = np.real(asltcx_spectrum)  # Real component for voice
    asltcx_mag_voice = asltcx_magnitude * asltcx_voice

    print(f"Computing DLOGST Spectral Descriptor for {trace_name}...")
    start_time = time.time()
    # Compute DLOGST
    MST, dlogst_mag, dlogst_phase, dlogst_voice, peak_freq, freq_loc, spec_centroid, \
    spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst = \
        dlogst_spec_descriptor(signal, dt, fmax_samples, shape, kmax, int_val)
    dlogst_time = time.time() - start_time

    dlogst_mag_voice = dlogst_mag * dlogst_voice

    # Print performance comparison
    print(f"Performance comparison for {trace_name}:")
    print(f"  ASLTCX execution time: {asltcx_time:.4f} seconds")
    print(f"  DLOGST execution time: {dlogst_time:.4f} seconds")
    print(f"  Speed ratio (DLOGST/ASLTCX): {dlogst_time/asltcx_time:.2f}x")

    # Limit frequency range for DLOGST to match ASLTCX range
    freq_limit = np.argmin(np.abs(freqst - freq_range[1]))

    # Create the 7-column plot
    fig, axes = plt.subplots(1, 7, figsize=(28, 10), sharey=True)
    fig.suptitle(f'ASLTCX vs DLOGST Comparison - {trace_name}', fontsize=16)

    # Column 1: Original Signal
    axes[0].plot(signal, time, color='black', linewidth=0.8)
    axes[0].set_title('Original Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].grid(True, alpha=0.3)
    axes[0].set_xlim(settings['Input Signal'])

    # Columns 2-4: ASLTCX results
    # Magnitude TFR
    im1 = axes[1].pcolormesh(freqs, time, asltcx_magnitude.T,
                            shading='auto', cmap=settings['colormap'])
    axes[1].set_title('ASLTCX Magnitude')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_xlim(freq_range)
    plt.colorbar(im1, ax=axes[1], shrink=0.8)

    # Voice TFR (real part with oscillation)
    vmax_asltcx = np.max(np.abs(asltcx_voice))
    im2 = axes[2].pcolormesh(freqs, time, asltcx_voice.T,
                            shading='auto', cmap='RdBu',
                            vmin=-vmax_asltcx, vmax=vmax_asltcx)
    axes[2].set_title('ASLTCX Voice')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_xlim(freq_range)
    plt.colorbar(im2, ax=axes[2], shrink=0.8)

    # Magnitude * Voice TFR
    im3 = axes[3].pcolormesh(freqs, time, asltcx_mag_voice.T,
                            shading='auto', cmap=settings['colormap'])
    axes[3].set_title('ASLTCX Mag×Voice')
    axes[3].set_xlabel('Frequency (Hz)')
    axes[3].set_xlim(freq_range)
    plt.colorbar(im3, ax=axes[3], shrink=0.8)

    # Columns 5-7: DLOGST results
    # Magnitude TFR
    im4 = axes[4].pcolormesh(freqst[:freq_limit], time,
                            dlogst_mag[:freq_limit, :].T,
                            shading='auto', cmap=settings['colormap'])
    axes[4].set_title('DLOGST Magnitude')
    axes[4].set_xlabel('Frequency (Hz)')
    axes[4].set_xlim(freq_range)
    plt.colorbar(im4, ax=axes[4], shrink=0.8)

    # Voice TFR
    vmax_dlogst = np.max(np.abs(dlogst_voice[:freq_limit, :]))
    im5 = axes[5].pcolormesh(freqst[:freq_limit], time,
                            dlogst_voice[:freq_limit, :].T,
                            shading='auto', cmap='RdBu',
                            vmin=-vmax_dlogst, vmax=vmax_dlogst)
    axes[5].set_title('DLOGST Voice')
    axes[5].set_xlabel('Frequency (Hz)')
    axes[5].set_xlim(freq_range)
    plt.colorbar(im5, ax=axes[5], shrink=0.8)

    # Magnitude * Voice TFR
    im6 = axes[6].pcolormesh(freqst[:freq_limit], time,
                            dlogst_mag_voice[:freq_limit, :].T,
                            shading='auto', cmap=settings['colormap'])
    axes[6].set_title('DLOGST Mag×Voice')
    axes[6].set_xlabel('Frequency (Hz)')
    axes[6].set_xlim(freq_range)
    plt.colorbar(im6, ax=axes[6], shrink=0.8)

    # Set time limits for all axes (invert for seismic convention)
    for ax in axes:
        ax.set_ylim(settings['Time (Y-axis)'][::-1])  # Reverse for seismic convention
        if ax != axes[0]:  # Don't set ylabel for signal plot
            ax.set_ylabel('')

    plt.tight_layout()
    plt.subplots_adjust(top=0.9)

    return fig, axes, asltcx_time, dlogst_time

def get_trace_indices(data_shape):
    """Get trace indices from user or use defaults."""
    root = tk.Tk()
    root.title("Select Trace Indices")

    trace_indices = {}

    def add_trace():
        name = name_entry.get().strip()
        try:
            index = int(index_entry.get())
            if 0 <= index < data_shape[0]:
                trace_indices[name] = index
                trace_listbox.insert(tk.END, f"{name}: {index}")
                name_entry.delete(0, tk.END)
                index_entry.delete(0, tk.END)
            else:
                tk.messagebox.showerror("Error", f"Index must be between 0 and {data_shape[0]-1}")
        except ValueError:
            tk.messagebox.showerror("Error", "Index must be a valid integer")

    def use_defaults():
        defaults = {
            'Trace_1': 0,
            'Trace_2': min(1, data_shape[0]-1),
            'Trace_3': min(2, data_shape[0]-1)
        }
        for name, idx in defaults.items():
            if idx < data_shape[0]:
                trace_indices[name] = idx
                trace_listbox.insert(tk.END, f"{name}: {idx}")

    def on_ok():
        root.quit()
        root.destroy()

    tk.Label(root, text=f"Available traces: 0 to {data_shape[0]-1}").pack(pady=5)

    # Input frame
    input_frame = tk.Frame(root)
    input_frame.pack(pady=10)

    tk.Label(input_frame, text="Trace Name:").grid(row=0, column=0, padx=5)
    name_entry = tk.Entry(input_frame)
    name_entry.grid(row=0, column=1, padx=5)

    tk.Label(input_frame, text="Index:").grid(row=0, column=2, padx=5)
    index_entry = tk.Entry(input_frame, width=10)
    index_entry.grid(row=0, column=3, padx=5)

    tk.Button(input_frame, text="Add", command=add_trace).grid(row=0, column=4, padx=5)

    # Listbox to show selected traces
    tk.Label(root, text="Selected Traces:").pack()
    trace_listbox = tk.Listbox(root, height=6)
    trace_listbox.pack(pady=5)

    # Buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)

    tk.Button(button_frame, text="Use Defaults", command=use_defaults).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)

    root.mainloop()
    return trace_indices

def print_performance_summary(results):
    """Print a summary of performance comparison results."""
    print("\n" + "="*60)
    print("PERFORMANCE COMPARISON SUMMARY")
    print("="*60)

    total_asltcx_time = sum(result['asltcx_time'] for result in results)
    total_dlogst_time = sum(result['dlogst_time'] for result in results)

    print(f"Total ASLTCX time: {total_asltcx_time:.4f} seconds")
    print(f"Total DLOGST time: {total_dlogst_time:.4f} seconds")
    print(f"Overall speed ratio (DLOGST/ASLTCX): {total_dlogst_time/total_asltcx_time:.2f}x")

    print("\nPer-trace breakdown:")
    for result in results:
        print(f"  {result['trace_name']}:")
        print(f"    ASLTCX: {result['asltcx_time']:.4f}s")
        print(f"    DLOGST: {result['dlogst_time']:.4f}s")
        print(f"    Ratio: {result['dlogst_time']/result['asltcx_time']:.2f}x")

    print("="*60)

def main():
    """Main function to process multiple traces and compare implementations."""
    try:
        # Choose data source
        data_source = get_data_source()

        if data_source == "segy":
            # Load SEG-Y data
            print("Loading SEG-Y data...")
            data, time, dt = load_segy_data()
        else:
            # Generate synthetic data
            print("Generating synthetic test data...")
            data, time, dt = generate_synthetic_data()

        fs = 1.0 / dt

        print(f"Loaded data:")
        print(f"  Number of traces: {data.shape[0]}")
        print(f"  Samples per trace: {data.shape[1]}")
        print(f"  Sampling interval: {dt*1000:.2f} ms")
        print(f"  Total time: {time[-1]:.2f} s")

        # Get trace indices from user
        trace_indices = get_trace_indices(data.shape)

        if not trace_indices:
            print("No traces selected. Exiting.")
            return

        # Get plot settings and parameters
        print("Getting plot settings...")
        settings = get_plot_settings()

        # Create a list to store all figure and axes objects for MultiCursor
        all_figs = []
        all_axs = []
        performance_results = []

        # Process each trace
        for trace_name, trace_index in trace_indices.items():
            print(f"\nProcessing trace {trace_name} (index {trace_index})...")
            trace_data = data[trace_index, :]

            # Create comparison plot
            fig, axes, asltcx_time, dlogst_time = create_7_column_comparison(
                trace_data, time, fs, trace_name, settings)
            all_figs.append(fig)
            all_axs.append(axes)

            # Store performance results
            performance_results.append({
                'trace_name': trace_name,
                'asltcx_time': asltcx_time,
                'dlogst_time': dlogst_time
            })

            # Save individual plot
            filename = f'asltcx_dlogst_comparison_{trace_name}.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"Plot saved as '{filename}'")

        # Apply MultiCursor to all figures for synchronized navigation
        if len(all_figs) > 1:
            all_axes = [ax for axs in all_axs for ax in axs]  # Flatten the list of axes
            multi = MultiCursor(plt.gcf().canvas, all_axes, color='r', lw=1, horizOn=True, vertOn=True)

            # Ensure the canvas is redrawn to avoid trailing lines
            for fig in all_figs:
                fig.canvas.draw_idle()

        # Print performance summary
        print_performance_summary(performance_results)

        print(f"\nProcessing complete. Generated {len(trace_indices)} comparison plots.")
        print(f"Time range: {time[0]:.2f}s to {time[-1]:.2f}s")
        print(f"Sampling rate: {fs:.1f}Hz")
        print("Comparing ASLTCX transform against DLOGST spectral descriptor.")

        plt.show()

    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
