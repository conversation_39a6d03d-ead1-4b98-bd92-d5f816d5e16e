#!/usr/bin/env python3
"""
Test script to verify FASLTCX integration and wrapper function compatibility.
This script tests the basic functionality without requiring SEG-Y files.
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the code and reference directories to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'code')
reference_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'reference')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)
if reference_dir not in sys.path:
    sys.path.insert(0, reference_dir)

try:
    from fasltcx import fasltcx
    print("✓ Successfully imported fasltcx")
except ImportError as e:
    print(f"✗ Failed to import fasltcx: {e}")
    sys.exit(1)

def superlets_fasltcx_wrapper(data, fs, foi, c1, ord):
    """Wrapper function to make fasltcx compatible with superletcx interface"""
    return fasltcx(data, fs, foi, c1, ord, True)

def create_test_signal(fs=1000, duration=2.0):
    """Create a test signal with multiple frequency components."""
    t = np.arange(0, duration, 1/fs)
    
    # Create a signal with multiple frequency components
    signal = (np.sin(2 * np.pi * 10 * t) +  # 10 Hz component
              0.5 * np.sin(2 * np.pi * 25 * t) +  # 25 Hz component
              0.3 * np.sin(2 * np.pi * 50 * t))   # 50 Hz component
    
    # Add some noise
    signal += 0.1 * np.random.randn(len(t))
    
    return signal, t

def test_fasltcx_basic():
    """Test basic FASLTCX functionality."""
    print("\n=== Testing Basic FASLTCX Functionality ===")
    
    # Create test signal
    fs = 1000  # 1 kHz sampling rate
    signal, time = create_test_signal(fs)
    
    # Define frequency range
    freqs = np.linspace(5, 100, 50)  # 5-100 Hz, 50 frequency bins
    
    # FASLTCX parameters
    c1 = 3
    orders = (1, 10)
    
    print(f"Signal length: {len(signal)} samples")
    print(f"Sampling rate: {fs} Hz")
    print(f"Frequency range: {freqs[0]:.1f} - {freqs[-1]:.1f} Hz")
    print(f"Number of frequencies: {len(freqs)}")
    print(f"Base cycles (c1): {c1}")
    print(f"Order range: {orders}")
    
    try:
        # Test direct fasltcx call
        print("\nTesting direct fasltcx call...")
        result_direct = fasltcx(signal, fs, freqs, c1, orders, True)
        print(f"✓ Direct call successful. Result shape: {result_direct.shape}")
        print(f"  Result dtype: {result_direct.dtype}")
        print(f"  Result is complex: {np.iscomplexobj(result_direct)}")
        
        # Test wrapper function
        print("\nTesting wrapper function...")
        result_wrapper = superlets_fasltcx_wrapper(signal, fs, freqs, c1, orders)
        print(f"✓ Wrapper call successful. Result shape: {result_wrapper.shape}")
        print(f"  Result dtype: {result_wrapper.dtype}")
        print(f"  Result is complex: {np.iscomplexobj(result_wrapper)}")
        
        # Verify results are identical
        if np.allclose(result_direct, result_wrapper):
            print("✓ Direct and wrapper results are identical")
        else:
            print("✗ Direct and wrapper results differ")
            
        return result_wrapper, time, freqs
        
    except Exception as e:
        print(f"✗ Error during FASLTCX computation: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_spectrum_components(spectrum, time, freqs):
    """Test extraction of magnitude and voice components."""
    print("\n=== Testing Spectrum Component Extraction ===")
    
    if spectrum is None:
        print("✗ No spectrum to test")
        return
    
    try:
        # Extract components
        magnitude = np.abs(spectrum)
        voice = np.real(spectrum)
        mag_voice = magnitude * voice
        
        print(f"✓ Magnitude extraction successful. Shape: {magnitude.shape}")
        print(f"  Magnitude range: {magnitude.min():.6f} - {magnitude.max():.6f}")
        
        print(f"✓ Voice extraction successful. Shape: {voice.shape}")
        print(f"  Voice range: {voice.min():.6f} - {voice.max():.6f}")
        
        print(f"✓ Magnitude×Voice calculation successful. Shape: {mag_voice.shape}")
        print(f"  Mag×Voice range: {mag_voice.min():.6f} - {mag_voice.max():.6f}")
        
        return magnitude, voice, mag_voice
        
    except Exception as e:
        print(f"✗ Error during component extraction: {e}")
        return None, None, None

def create_test_plot(signal, time, spectrum, freqs):
    """Create a test plot to visualize results."""
    print("\n=== Creating Test Plot ===")
    
    if spectrum is None:
        print("✗ No spectrum to plot")
        return
    
    try:
        magnitude = np.abs(spectrum)
        voice = np.real(spectrum)
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Plot 1: Original signal
        axes[0].plot(time, signal)
        axes[0].set_title('Test Signal')
        axes[0].set_xlabel('Time (s)')
        axes[0].set_ylabel('Amplitude')
        axes[0].grid(True, alpha=0.3)
        
        # Plot 2: Magnitude spectrogram
        im1 = axes[1].pcolormesh(freqs, time, magnitude.T, shading='auto', cmap='viridis')
        axes[1].set_title('FASLTCX Magnitude')
        axes[1].set_xlabel('Frequency (Hz)')
        axes[1].set_ylabel('Time (s)')
        plt.colorbar(im1, ax=axes[1])
        
        # Plot 3: Voice spectrogram
        vmax = np.max(np.abs(voice))
        im2 = axes[2].pcolormesh(freqs, time, voice.T, shading='auto', cmap='RdBu', 
                                vmin=-vmax, vmax=vmax)
        axes[2].set_title('FASLTCX Voice')
        axes[2].set_xlabel('Frequency (Hz)')
        axes[2].set_ylabel('Time (s)')
        plt.colorbar(im2, ax=axes[2])
        
        plt.tight_layout()
        plt.savefig('fasltcx_test_results.png', dpi=150, bbox_inches='tight')
        print("✓ Test plot saved as 'fasltcx_test_results.png'")
        
        # Don't show the plot automatically to avoid blocking in automated tests
        # plt.show()
        
    except Exception as e:
        print(f"✗ Error creating test plot: {e}")

def main():
    """Main test function."""
    print("FASLTCX Integration Test")
    print("=" * 50)
    
    # Test basic functionality
    spectrum, time, freqs = test_fasltcx_basic()
    
    # Test component extraction
    magnitude, voice, mag_voice = test_spectrum_components(spectrum, time, freqs)
    
    # Create test signal for plotting
    signal, _ = create_test_signal()
    
    # Create test plot
    create_test_plot(signal, time, spectrum, freqs)
    
    print("\n" + "=" * 50)
    if spectrum is not None:
        print("✓ All tests completed successfully!")
        print("The FASLTCX integration is working correctly.")
        print("You can now use the demo scripts with confidence.")
    else:
        print("✗ Tests failed. Please check the error messages above.")
    
    print("\nNext steps:")
    print("1. Run the demo scripts: segy_superlet_dlogst_comparison.py or segy_fasltcx_dlogst_comparison.py")
    print("2. Load a SEG-Y file to test with real seismic data")
    print("3. Compare results with the original superlet implementation")

if __name__ == "__main__":
    main()
